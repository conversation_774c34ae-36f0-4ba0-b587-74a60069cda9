# CRUD Application with Vue.js and Node.js

This is a simple CRUD application built using Vue.js for the frontend and Node.js with <PERSON> for the backend. The application uses MongoDB Atlas as the database to store and manage data.

## Project Structure

```
vue-crud-app
├── client                # Frontend application
│   ├── src               # Source files for Vue.js
│   ├── package.json      # Client dependencies and scripts
│   └── README.md         # Client documentation
├── server                # Backend application
│   ├── src               # Source files for Node.js
│   ├── package.json      # Server dependencies and scripts
│   └── README.md         # Server documentation
└── README.md             # Overall project documentation
```

## Getting Started

### Prerequisites

- Node.js and npm installed on your machine.
- MongoDB Atlas account and a cluster set up.

### Server Setup

1. Navigate to the `server` directory:
   ```
   cd server
   ```

2. Install the server dependencies:
   ```
   npm install
   ```

3. Set up your MongoDB connection string in the `src/app.js` file.

4. Start the server:
   ```
   npm start
   ```

### Client Setup

1. Navigate to the `client` directory:
   ```
   cd client
   ```

2. Install the client dependencies:
   ```
   npm install
   ```

3. Start the client application:
   ```
   npm run serve
   ```

## Usage

- The application allows users to create, read, update, and delete items.
- Access the frontend application in your browser at `http://localhost:8080` (or the port specified in your Vue configuration).
- The backend API can be accessed at `http://localhost:3000` (or the port specified in your Node.js configuration).

## Contributing

Feel free to submit issues or pull requests for improvements or bug fixes. 

## License

This project is licensed under the MIT License.