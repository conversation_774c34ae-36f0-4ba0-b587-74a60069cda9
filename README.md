# Vue CRUD Application

This project is a simple CRUD (Create, Read, Update, Delete) application built with Vue.js for the client-side and Node.js with MongoDB Atlas for the server-side.

## Project Structure

```
vue-crud-app
├── client          # Client-side application
│   ├── src
│   │   ├── App.vue
│   │   ├── main.js
│   │   ├── components
│   │   │   └── CrudComponent.vue
│   │   └── views
│   │       └── Home.vue
│   ├── package.json
│   └── README.md
├── server          # Server-side application
│   ├── src
│   │   ├── app.js
│   │   ├── controllers
│   │   │   └── crudController.js
│   │   ├── models
│   │   │   └── itemModel.js
│   │   └── routes
│   │       └── crudRoutes.js
│   ├── package.json
│   └── README.md
└── README.md       # Overall project documentation
```

## Getting Started

### Prerequisites

- Node.js
- npm (Node Package Manager)
- MongoDB Atlas account

### Client Setup

1. Navigate to the `client` directory:
   ```
   cd client
   ```

2. Install the dependencies:
   ```
   npm install
   ```

3. Run the client application:
   ```
   npm run serve
   ```

### Server Setup

1. Navigate to the `server` directory:
   ```
   cd server
   ```

2. Install the dependencies:
   ```
   npm install
   ```

3. Set up your MongoDB Atlas connection string in `server/src/app.js`.

4. Run the server application:
   ```
   npm start
   ```

## Usage

- Access the client application in your browser at `http://localhost:8080`.
- The server API will be available at `http://localhost:3000`.

## Features

- Create, read, update, and delete items.
- Responsive design using Vue.js.
- RESTful API built with Node.js and Express.

## License

This project is licensed under the MIT License.