{"name": "vue-crud-app-server", "version": "1.0.0", "description": "Server for Vue CRUD application using Node.js and MongoDB Atlas", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^8.2.0", "express": "^4.21.2", "mongoose": "^5.13.23", "pg-sdk-node": "https://phonepe.mycloudrepo.io/public/repositories/phonepe-pg-sdk-node/releases/v2/phonepe-pg-sdk-node.tgz"}, "devDependencies": {"nodemon": "^2.0.4"}, "engines": {"node": ">=12.0.0"}, "keywords": [], "author": "", "license": "ISC"}