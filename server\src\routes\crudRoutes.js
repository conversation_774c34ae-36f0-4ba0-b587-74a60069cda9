const express = require('express');
const router = express.Router();
const CrudController = require('../controllers/crudController');

const crudController = new CrudController();

// Define routes for CRUD operations
router.post('/items', crudController.createItem);
router.get('/items', crudController.getItems);
router.put('/items/:id', crudController.updateItem);
router.delete('/items/:id', crudController.deleteItem);

module.exports = router;