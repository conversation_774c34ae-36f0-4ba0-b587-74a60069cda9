const express = require('express');
const mongoose = require('mongoose');
const bodyParser = require('body-parser');
const crudRoutes = require('./routes/crudRoutes');

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(bodyParser.json());
app.use('/api', crudRoutes);

// MongoDB Atlas connection
const MONGO_URI = process.env.MONGO_URI; // Ensure to set this in your environment variables
mongoose.connect(MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true })
    .then(() => console.log('MongoDB connected'))
    .catch(err => console.error('MongoDB connection error:', err));

// Start the server
app.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
});